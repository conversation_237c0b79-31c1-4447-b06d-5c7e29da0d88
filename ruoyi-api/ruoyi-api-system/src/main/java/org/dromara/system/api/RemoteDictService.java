package org.dromara.system.api;

import org.dromara.system.api.domain.vo.RemoteDictDataVo;
import org.dromara.system.api.domain.vo.RemoteDictTypeVo;

import java.util.List;
import java.util.Map;

/**
 * 字典服务
 *
 * <AUTHOR> Li
 */
public interface RemoteDictService {

    /**
     * 根据字典类型查询信息
     *
     * @param dictType 字典类型
     * @return 字典类型
     */
    RemoteDictTypeVo selectDictTypeByType(String dictType);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    List<RemoteDictDataVo> selectDictDataByType(String dictType);

    /**
     * 根据类型和键值获取对应语言的标签
     * @param dictType
     * @param dictValue
     * @return
     */
    String selectDictLabel(String dictType, String dictValue);

    /**
     * 根据字典类型查询当前语言的字典数据
     *
     * @param dictType 字典类型
     * @return Map  Key：dictValue  Value：dictLabel
     */
    Map<String, String> selectLocalizedDictDataByType(String dictType);

}
