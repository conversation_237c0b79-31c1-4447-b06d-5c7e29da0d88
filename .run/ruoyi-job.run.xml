<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ruoyi-job" type="docker-deploy" factoryName="dockerfile" server-name="payprod">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="ruoyi/ruoyi-job:2.4.0" />
        <option name="buildOnly" value="true" />
        <option name="sourceFilePath" value="ruoyi-modules/ruoyi-job/Dockerfile" />
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>
