package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.system.domain.vo.SysDictDataI18nVo;

/**
 * 字典数据多语言扩展表
 */
@Data
@AutoMappers({
    @AutoMapper(target = SysDictDataI18nVo.class)
})
@TableName("sys_dict_data_i18n")
public class SysDictDataI18n extends TenantEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 关联字典编码
     */
    private Long dictCode;
    /**
     * 语言标签(如zh-CN, en-US)
     */
    private String languageTag;
    /**
     * 多语言字典标签
     */
    private String dictLabel;
    /**
     * 多语言字典键值
     */
    private String dictValue;
    /**
     * 多语言备注
     */
    private String remark;

}
