package org.dromara.acct.domain.bo;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.dromara.common.core.validate.coin.FundsOperation;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PayUserInfoBo {

    /**
     * 节点等级
     */
    private String nodeLevel;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { FundsOperation.class })
    private Long userId;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { FundsOperation.class })
    @Positive(message = "金额必须大于0", groups = { FundsOperation.class })
    private BigDecimal amount;

    /**
     * 用户ID列表
     */
    List<Long> userIds;
}
