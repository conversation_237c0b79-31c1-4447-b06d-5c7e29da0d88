package org.dromara.acct.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.aset.api.domain.vo.RemoteCoinVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/19
 */
@Data
public class PayUserInfoVo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账号名称(目前与邮箱也一致)
     */
    private String userName;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * usd余额
     */
    private BigDecimal usdBalance;

    /**
     * 冻结的usd余额
     */
    private BigDecimal freezeUsdBalance;

    /**
     * 银卡激活码数量
     */
    private Integer silverActiveCodeNum;

    /**
     * 金卡激活码数量
     */
    private Integer goldenActiveCodeNum;

    /**
     * 黑卡激活码数量
     */
    private Integer goldenblackActiveCodeNum;

    /**
     * 状态
     */
    private Character status;

    /**
     * 节点等级 默认为 00-无
     */
    private String nodeLevel;

    /**
     * 节点等级标签
     */
    private String nodeLevelLabel;

    /**
     * 渠道名字
     */
    private String channelName;

    /**
     * 交易受限到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tradeLimitExpired;

    /**
     * 登录受限到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date loginLimitExpired;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 客户钱包列表
     */
    private List<RemoteCoinVo> coinBalanceList;

    /**
     * 渠道用户ID
     */
    private Long channelUserId;
}
