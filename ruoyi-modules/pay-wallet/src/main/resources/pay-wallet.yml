# pay-wallet 钱包服务配置
spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      seata: false
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.pay-wallet.url}
          username: ${datasource.pay-wallet.username}
          password: ${datasource.pay-wallet.password}

snail-job:
  enabled: true
  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
  group: "pay_wallet_group"
  #  SnailJob 接入验证令牌
  token: "SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj"
  server:
    # 从 nacos 获取服务
    server-name: ruoyi-snailjob-server
    # 服务名优先 ip垫底
    #host: **************
    port: 17888
  # 详见 sql/ry_job.sql `sj_namespace` 表 `unique_id`
  namespace: ${spring.profiles.active}
  # 随主应用端口飘逸
  port: 2${server.port}
  # 客户端ip指定
  host: **************
  # RPC类型: netty, grpc
  rpc-type: grpc

# SOLANA 配置
solana:
  # 钱包相关配置
  wallet:
    monitor: false
    compensation: false
    main-address-list:
      - tenant-id: "*********"
        address: "65uLDf8bh8JjnJ48bkcVVbyapNgjvZURJU8Dah4HfCCz"
      - tenant-id: "system2"
        address: "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
      - tenant-id: "99"
        address: "65uLDf8bh8JjnJ48bkcVVbyapNgjvZURJU8Dah4HfCCz"
  # RPC相关配置
  rpc:
    rpcList:
      - https://aged-chaotic-telescope.solana-devnet.quiknode.pro/
    websocketUrl: wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/
  # 监控相关配置
  monitor:
    # 监控总开关 - 控制是否启用整个监控功能
    enabled: true
  # 定时补偿配置
  compensation:
    # 总开关 - 控制整个补偿功能的启用状态
    enabled: false
    # 重启时执行开关 - 控制是否在应用重启时立即执行一次补偿
    on-startup: false
  # 合约相关配置
  contract:
    contracts:
      sol:
        address: "So11111111111111111111111111111111111111112"
        decimals: 9
      usdt:
        address: "Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr"
        decimals: 6
      usdc:
        address: "4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU"
        decimals: 6

# TRON 配置
tron:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "TBCDpHjD6KdUgJJJ9jxkYNKiGfjsFTiDYU"
      - tenant-id: "system2"
        address: "TMuA6YqfCeX8EhbfYEg5y7S4DqzSJireY9"
    fee-wallet:
      enabled: true
      private-key: "5fd1cd1a7ca6f2b4ce1bf6fce73e21ba8972c4a16216740c8763f09a8bf3be12"
      address: "TJEv4KfgVafkvTqpjsLr97MhQfzDLdYtqJ"
      # 统一费用控制配置
      max-fee-limit: 5000000        # 5 TRX (最大费用限制)
      max-energy-burn: 50000        # 最大能量燃烧限制
      auto-burn-enabled: true       # 自动燃烧TRX支付能量
      auto-burn-bandwidth-enabled: true  # 自动燃烧TRX支付带宽
      # 能量代理API配置
      energy-proxy-enabled: false   # 是否启用能量代理API功能
      energy-proxy-url: ""          # 能量代理API地址，如：http://api.example.com/energy
      energy-proxy-key: "key"       # API密钥参数名
      energy-proxy-hour: 1          # 能量提供小时数
      energy-proxy-value: ""        # API密钥值

  # 统一API配置（合并RPC和HTTP配置）
  api:
    enabled: true
    mode: "http"  # API模式：rpc 或 http
    network-type: "TESTNET"
    primary-endpoint: "https://api.shasta.trongrid.io"
    backup-endpoints:
#      - "https://api.shasta.tronex.io"
    scan-endpoint: "https://shastapi.tronscan.org"
    api-keys:
      - "4081171d-142f-4428-ad11-875e14a7fb3a"
    connection-timeout: 30000  # 连接超时（毫秒）- 建立TCP连接的时间限制
    read-timeout: 60000        # 读取超时（毫秒）- 等待服务器响应的时间限制
    user-agent: "TronWallet/1.0"  # 用户代理 - 帮助API识别客户端类型

  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），TRON出块时间约3秒，建议15秒

  contract:
    enabled: true
    contracts:
      usdt:
        address: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
        decimals: 6
        description: "USDT测试币"
        enabled: true
        min-amount: 1.0
        max-amount: 10000.0
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "TWxMbmt8ubE1Me6fF2C13jGC39ZtxN6PVe"
        decimals: 6
        description: "USDC测试币"
        enabled: true
        min-amount: 1.0
        max-amount: 5000.0
        symbol: "USDC"
        name: "USD Coin"

# BSC 配置
bsc:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "******************************************"
    fee-wallet:
      enabled: true
      private-key: "1b4a7cde58f6796916b7b970b8d835a6795654de6233f1085b29c2821046f30f"
      address: "******************************************"
      # 统一费用控制配置
      max-gas-price: 20000000000  # 20 gwei (BSC主网安全上限)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "TESTNET"
    chain-id: 97  # BSC测试网
    endpoint: "https://capable-sleek-sponge.bsc-testnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/"
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"
        decimals: 18
        description: "USDT测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 10000.0
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 18
        description: "USDC测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 5000.0
        symbol: "USDC"
        name: "USD Coin"
    bridge-enabled: false
    staking-enabled: false

  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），BSC出块时间约3秒，建议15秒

# ARB (Arbitrum) 配置
arb:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "******************************************"
    fee-wallet:
      enabled: true
      private-key: "${ARB_FEE_WALLET_PRIVATE_KEY:******************************************123456789012345678901234}"
      address: "******************************************"
      # 统一费用控制配置
      max-gas-price: 1000000000   # 1 gwei (Layer 2更便宜)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "TESTNET"
    chain-id: 421614  # Arbitrum测试网
    endpoint: "https://aged-chaotic-telescope.arbitrum-sepolia.quiknode.pro/"
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"
        decimals: 6
        description: "USDT测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 10000.0
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 6
        description: "USDC测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 5000.0
        symbol: "USDC"
        name: "USD Coin"
    bridge-enabled: false
    staking-enabled: false

  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），ARB出块时间约0.25秒，建议15秒

# BASE 配置
base:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "******************************************"
    fee-wallet:
      enabled: true
      private-key: "${BASE_FEE_WALLET_PRIVATE_KEY:******************************************123456789012345678901234}"
      address: "******************************************"
      # 统一费用控制配置
      max-gas-price: 1000000000   # 1 gwei (Layer 2更便宜)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "TESTNET"
    chain-id: 84532  # Base测试网
    endpoint: "https://capable-sleek-sponge.base-sepolia.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b"
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"
        decimals: 6
        description: "USDT测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 10000.0
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 6
        description: "USDC测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 5000.0
        symbol: "USDC"
        name: "USD Coin"
    bridge-enabled: false
    staking-enabled: false

  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: true          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 15000          # 自动扫描周期（毫秒），BASE出块时间约2秒，建议15秒

# AVAX (Avalanche) 配置
avax:
  wallet:
    enabled: true
    main-address-list:
      - tenant-id: "*********"
        address: "******************************************"
    fee-wallet:
      enabled: true
      private-key: "${AVAX_FEE_WALLET_PRIVATE_KEY:******************************************123456789012345678901234}"
      address: "******************************************"
      # 统一费用控制配置
      max-gas-price: 30000000000  # 30 gwei (AVAX网络通常比BSC稍高)
      max-gas-limit: 8000000      # 8M gas (区块gas限制)

  rpc:
    enabled: true
    network-type: "TESTNET"
    chain-id: 43113  # AVAX Fuji测试网
    endpoint: "https://api.avax-test.network/ext/bc/C/rpc"
    connection-timeout: 30
    read-timeout: 60

  contract:
    enabled: true
    contracts:
      usdt:
        address: "******************************************"  # 测试网暂无USDT，使用占位符
        decimals: 6
        description: "USDT测试币（暂无）"
        enabled: false
        min-amount: 0.01
        max-amount: 10000.0
        symbol: "USDT"
        name: "Tether USD"
      usdc:
        address: "******************************************"
        decimals: 6
        description: "USDC测试币"
        enabled: true
        min-amount: 0.01
        max-amount: 5000.0
        symbol: "USDC"
        name: "USD Coin"
    bridge-enabled: false
    staking-enabled: false

  # 监控配置
  monitor:
    # 自动扫描配置
    auto-scan-enabled: false          # 是否启用自动扫描功能
    auto-scan-start-block: 0         # 自动扫描起始区块号（0表示从Redis恢复或最新区块开始）
    auto-scan-period: 8000           # 自动扫描周期（毫秒），AVAX出块时间约2秒，建议6秒

# 私钥加密配置
wallet-encryption:
  # 是否启用加密功能
  enabled: true
  # 加密密钥 (生产环境请使用环境变量或更安全的方式配置)
  secret-key: "${WALLET_ENCRYPTION_SECRET_KEY:MetaWallet123456}"
  # 加密算法 支持: AES, DES, 3DES 等
  algorithm: "AES"
  # 编码方式 支持: BASE64, HEX
  encoding: "BASE64"
