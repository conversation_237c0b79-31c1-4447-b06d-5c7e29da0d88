package org.dromara.wallet.monitor.evm.schedule;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.BscConfigFacade;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.springframework.stereotype.Component;

/**
 * BSC（Binance Smart Chain）链区块扫描调度任务
 * 负责定期扫描BSC链的区块，获取交易记录并进行处理
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>BSC链专用的区块扫描调度</li>
 *   <li>区块进度管理和持久化</li>
 *   <li>交易记录扫描和处理</li>
 *   <li>完整的错误处理和重试机制</li>
 *   <li>详细的执行统计信息</li>
 * </ul>
 *
 * <p>配置要求：</p>
 * <ul>
 *   <li>BSC链配置必须启用（bscConfigFacade.isEnabled() = true）</li>
 *   <li>RPC端点配置正确且可访问</li>
 *   <li>代币合约地址配置完整</li>
 *   <li>Redis连接正常用于存储扫描进度</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/11
 **/
@Slf4j
@Component
@JobExecutor(name = "bscScanSchedule")
@RequiredArgsConstructor
public class BscScanSchedule extends AbstractEvmScanSchedule {

    private final BscConfigFacade bscConfigFacade;

    /**
     * Redis中存储BSC链当前处理区块的键名
     */
    private static final String BSC_CURRENT_BLOCK_KEY = "bsc_current_block";

    /**
     * BSC链名称标识
     */
    private static final String CHAIN_NAME = "BSC";

    /**
     * 执行BSC链区块扫描任务
     *
     * @param jobArgs 任务参数
     * @return 执行结果，包含详细的执行统计信息
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        log.debug("开始执行BSC链区块扫描调度任务");
        return super.jobExecute(jobArgs);
    }

    /**
     * 获取BSC链配置门面
     *
     * @return BSC配置门面实例
     */
    @Override
    protected EvmConfigFacade getConfigFacade() {
        return bscConfigFacade;
    }

    /**
     * 获取BSC链Redis存储键名
     *
     * @return Redis键名
     */
    @Override
    protected String getRedisKey() {
        return BSC_CURRENT_BLOCK_KEY;
    }

    /**
     * 获取BSC链名称
     *
     * @return 链名称
     */
    @Override
    protected String getChainName() {
        return CHAIN_NAME;
    }
}
