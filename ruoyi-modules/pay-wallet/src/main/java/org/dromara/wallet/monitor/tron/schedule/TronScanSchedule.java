package org.dromara.wallet.monitor.tron.schedule;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.springframework.stereotype.Component;

/**
 * TRON主网区块扫描调度任务
 * 负责定期扫描TRON主网的区块，获取交易记录并进行处理
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>TRON主网专用的区块扫描调度</li>
 *   <li>区块进度管理和持久化</li>
 *   <li>交易记录扫描和处理</li>
 *   <li>完整的错误处理和重试机制</li>
 *   <li>详细的执行统计信息</li>
 * </ul>
 *
 * <p>配置要求：</p>
 * <ul>
 *   <li>TRON链配置必须启用（tronConfigFacade.isEnabled() = true）</li>
 *   <li>HTTP API端点配置正确且可访问</li>
 *   <li>代币合约地址配置完整</li>
 *   <li>Redis连接正常用于存储扫描进度</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/11
 **/
@Slf4j
@Component
@JobExecutor(name = "tronScanSchedule")
@RequiredArgsConstructor
public class TronScanSchedule extends AbstractTronScanSchedule {

    private final TronConfigFacade tronConfigFacade;

    /**
     * Redis中存储TRON主网当前处理区块的键名
     */
    private static final String TRON_CURRENT_BLOCK_KEY = "tron_current_block";

    /**
     * TRON主网链名称标识
     */
    private static final String CHAIN_NAME = "TRON";

    /**
     * 执行TRON主网区块扫描任务
     *
     * @param jobArgs 任务参数
     * @return 执行结果，包含详细的执行统计信息
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        log.debug("开始执行TRON主网区块扫描调度任务");
        return super.jobExecute(jobArgs);
    }

    /**
     * 获取TRON主网配置门面
     *
     * @return TRON配置门面实例
     */
    @Override
    protected TronConfigFacade getConfigFacade() {
        return tronConfigFacade;
    }

    /**
     * 获取TRON主网Redis存储键名
     *
     * @return Redis键名
     */
    @Override
    protected String getRedisKey() {
        return TRON_CURRENT_BLOCK_KEY;
    }

    /**
     * 获取TRON主网链名称
     *
     * @return 链名称
     */
    @Override
    protected String getChainName() {
        return CHAIN_NAME;
    }
}
