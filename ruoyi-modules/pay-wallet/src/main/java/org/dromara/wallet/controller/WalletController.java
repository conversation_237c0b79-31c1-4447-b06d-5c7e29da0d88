package org.dromara.wallet.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.wallet.domain.dto.ImportBalanceRequest;
import org.dromara.wallet.domain.dto.WalletCommonBo;
import org.dromara.wallet.domain.dto.WalletCommonVo;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;
import org.dromara.wallet.event.BalanceChangeEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 14:46
 **/
@RestController
@RequestMapping("/common")
@RequiredArgsConstructor
public class WalletController {
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 归集功能
     * 归集不需要私钥和目标钱包
     */
    @SaCheckPermission("wallet:trc20address:collect")
    @Log(title = "后台区块链钱包归集", businessType = BusinessType.COLLECT)
    @PostMapping("/collect")
    public R<WalletCommonVo> collect(@RequestBody WalletCommonBo walletCommonBo) {
        // TODO: 获取主钱包地址，暂时写死（注意多租户）
        String effectiveToAddress = "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE";

        try {
            // 发布余额变更事件
            eventPublisher.publishEvent(new BalanceChangeEvent(this, walletCommonBo.getChainType(), walletCommonBo.getFromAddress(), null));

            WalletCommonVo response = WalletCommonVo.success(
                    WalletCommonVo.OperationType.COLLECT,
                    walletCommonBo.getChainType(),
                    walletCommonBo.getTokenSymbol(),
                    walletCommonBo.getAmount()
                ).withTransaction(null, walletCommonBo.getFromAddress(), effectiveToAddress)
                .withMemo(walletCommonBo.getMemo());

            return R.ok(response);
        } catch (Exception e) {
            WalletCommonVo response = WalletCommonVo.failure(WalletCommonVo.OperationType.COLLECT, e.getMessage());
            return R.fail(response);
        }
    }

    /**
     * 转账功能
     */
    @SaCheckPermission("wallet:trc20address:transfer")
    @Log(title = "后台区块链钱包转账", businessType = BusinessType.TRANSFER)
    @PostMapping("/transfer")
    public R<WalletCommonVo> transfer(@RequestBody WalletCommonBo walletCommonBo) {
        try {
//            // 根据链类型选择配置
//            TokenConfig tokenConfig;
//            if (walletCommonBo.getChainType() == ChainType.TRON) {
//                tokenConfig = tronChainConfig.getTokenRegistry().getToken(walletCommonBo.getTokenSymbol())
//                    .orElseThrow(() -> new IllegalArgumentException("V3配置中不支持的代币: " + walletCommonBo.getTokenSymbol()));
//
//                // 执行TRON转账
//                tronHelper.transferTokenWithAutoFee(
//                    walletCommonBo.getFromAddress(),
//                    walletCommonBo.getFromPrivateKey(),
//                    tokenConfig.toRawAmount(walletCommonBo.getAmount()),
//                    walletCommonBo.getToAddress(),
//                    tokenConfig.getAddress()
//                );
//            } else {
//                throw new IllegalArgumentException("暂不支持的链类型: " + walletCommonBo.getChainType());
//            }
//
//            // 发布余额变更事件
//            eventPublisher.publishEvent(new BalanceChangeEvent(this, walletCommonBo.getChainType(), walletCommonBo.getFromAddress(), null));
//
//            WalletCommonVo response = WalletCommonVo.success(
//                    WalletCommonVo.OperationType.TRANSFER,
//                    walletCommonBo.getChainType(),
//                    walletCommonBo.getTokenSymbol(),
//                    walletCommonBo.getAmount()
//                ).withTransaction(null, walletCommonBo.getFromAddress(), walletCommonBo.getToAddress())
//                .withMemo(walletCommonBo.getMemo());
//
//            return R.ok(response);

            return R.ok();
        } catch (Exception e) {
            WalletCommonVo response = WalletCommonVo.failure(WalletCommonVo.OperationType.TRANSFER, e.getMessage());
            return R.fail(response);
        }
    }

    /**
     * 查询未入账的交易记录
     */
    @SaCheckPermission("wallet:trc20address:transfer")
    @GetMapping("/queryUnconfirmed")
    public R<List<WalletCoinRecVo>> queryUnconfirmed() {
        // TODO: 实现查询未入账的交易记录逻辑
        return R.ok();
    }

    /**
     * 手动入账功能
     */
    @SaCheckPermission("wallet:trc20address:transfer")
    @Log(title = "后台区块链钱包手动入账", businessType = BusinessType.IMPORT)
    @PostMapping("/importCoinBalance")
    public R<WalletCommonVo> importCoinBalance(@RequestBody ImportBalanceRequest request) {
        try {
            // 验证请求参数
            request.validate();

            // TODO: 实现手动入账逻辑
            // 1. 验证钱包地址和私钥的有效性
            // 2. 检查链上实际余额（如果不是强制入账）
            // 3. 更新数据库余额记录
            // 4. 记录入账日志

            // 创建成功响应
            WalletCommonVo response = WalletCommonVo.success(
                    WalletCommonVo.OperationType.IMPORT_BALANCE,
                    request.getChainType(),
                    request.getTokenSymbol(),
                    request.getAmount()
                ).withTransaction(null, null, request.getAddress())
                .withMemo(request.getReason());

            return R.ok(response);

        } catch (Exception e) {
            WalletCommonVo response = WalletCommonVo.failure(
                WalletCommonVo.OperationType.IMPORT_BALANCE,
                e.getMessage()
            );
            return R.fail(response);
        }
    }

}
