package org.dromara.wallet.wallet.helper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * Aptos交易解析演示类
 *
 * 使用您提供的真实交易数据进行解析演示
 * 包含两种不同类型的交易：Swap交易和Transfer交易
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
public class AptosParserDemo {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final AptosTransactionParser parser = new AptosTransactionParser();

    public static void main(String[] args) {
        System.out.println("=== Aptos交易解析演示 ===\n");

        // 演示1：Swap交易解析
        demonstrateSwapTransaction();

        System.out.println("\n" + "=".repeat(80) + "\n");

        // 演示2：Transfer交易解析
        demonstrateTransferTransaction();
    }

    /**
     * 演示Swap交易解析
     */
    private static void demonstrateSwapTransaction() {
        System.out.println("【演示1：Swap交易解析】");
        System.out.println("交易类型：代币交换 (Token Swap)");
        System.out.println("交易哈希：0x4f11877d33f31b0a91eb23a11faeaf7d41741da77697588712c74189e4db9337");
        System.out.println();

        // 使用您提供的第一个交易数据（Swap交易）
        String swapTransactionJson = """
            {
              "version": "3173448286",
              "hash": "0x4f11877d33f31b0a91eb23a11faeaf7d41741da77697588712c74189e4db9337",
              "state_change_hash": "0x226bf59307f0c7cbb34bcdbb9a7d9c0d0309cddb59b4f7a6ed41faf34bd42473",
              "event_root_hash": "0x0d98ed72684fd2d765424bc400619424d4a83ad4b567fe0cd4b2e8b6039c0723",
              "state_checkpoint_hash": null,
              "gas_used": "52",
              "success": true,
              "vm_status": "Executed successfully",
              "accumulator_root_hash": "0x78c4ab703fbffb03c4b4f18ee1d24495e90528eb9766727df6ab75933018d0be",
              "sender": "0xf991631f78847f14c239d0f4758cb9b69e366828decb8438319c6b84116be063",
              "sequence_number": "20454",
              "max_gas_amount": "200000",
              "gas_unit_price": "100",
              "expiration_timestamp_secs": "1754273732",
              "payload": {
                "function": "0x487e905f899ccb6d46fdaec56ba1e0c4cf119862a16c409904b8c78fab1f5e8a::router::swap",
                "type_arguments": [],
                "arguments": ["0x82e0b52f95ae57b35220726a32c3415919389aa5b8baa33a058d7125797535cc00000000000000000100000000000000d88f5b000000000000000000000000000000000000000000000000000000000066f7560000000000000000000000000000000000000000000000000000000000"],
                "type": "entry_function_payload"
              },
              "signature": {
                "public_key": "0xfee26bc420ed01db3aa6bca4abb402699bacefa0b130d7ea65e324215bc3371a",
                "signature": "0x926d0e8dfb4bbfe6d33c883f78d859f2f18e266b3fbfd4652bfc8f96c834359affd2f1e64234a36e56760030b18167cad384310c7a0e3345fe01aef0a7832306",
                "type": "ed25519_signature"
              },
              "events": [
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::fungible_asset::Withdraw",
                  "data": {
                    "amount": "6000402",
                    "store": "0xd49ac8ab4cc24d71b5164d4c02b4a38aedb95759853a4bdb77469bcadd042b6f"
                  }
                },
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::fungible_asset::Deposit",
                  "data": {
                    "amount": "6000402",
                    "store": "0x8984a9c273ecf1c4d4c05357daa4961c260653c3b2b305cb9ca331a6df23433a"
                  }
                },
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::fungible_asset::Withdraw",
                  "data": {
                    "amount": "5999548",
                    "store": "0x52eb213a62b79674b6764ab515fea2e399508539f5de4cab439603967e564aee"
                  }
                },
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::fungible_asset::Deposit",
                  "data": {
                    "amount": "5999548",
                    "store": "0xb3f4d0255a62f3737bb25cb9d782a6c246e9592d4d4a8846dd1c6bb2fc3f0c5e"
                  }
                },
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x487e905f899ccb6d46fdaec56ba1e0c4cf119862a16c409904b8c78fab1f5e8a::router::Swapped",
                  "data": {
                    "amount_in": "6000402",
                    "amount_out": "5999548",
                    "asset_in_index": "0",
                    "asset_out_index": "1",
                    "assets": [
                      "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b",
                      "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b"
                    ],
                    "creator": "0xf991631f78847f14c239d0f4758cb9b69e366828decb8438319c6b84116be063",
                    "pool_addr": "0x82e0b52f95ae57b35220726a32c3415919389aa5b8baa33a058d7125797535cc",
                    "ts": "****************"
                  }
                }
              ],
              "changes": [
                {
                  "address": "0xd49ac8ab4cc24d71b5164d4c02b4a38aedb95759853a4bdb77469bcadd042b6f",
                  "state_key_hash": "0x8f86d369efb540500d65cfb0923ce8c587f1e05465dc2b61eb86e4d8d4ef601c",
                  "data": {
                    "type": "0x1::fungible_asset::FungibleStore",
                    "data": {
                      "balance": "2366555970",
                      "frozen": false,
                      "metadata": {
                        "inner": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b"
                      }
                    }
                  },
                  "type": "write_resource"
                },
                {
                  "address": "0xd49ac8ab4cc24d71b5164d4c02b4a38aedb95759853a4bdb77469bcadd042b6f",
                  "state_key_hash": "0x8f86d369efb540500d65cfb0923ce8c587f1e05465dc2b61eb86e4d8d4ef601c",
                  "data": {
                    "type": "0x1::object::ObjectCore",
                    "data": {
                      "allow_ungated_transfer": false,
                      "guid_creation_num": "1125899906842625",
                      "owner": "0xf991631f78847f14c239d0f4758cb9b69e366828decb8438319c6b84116be063",
                      "transfer_events": {
                        "counter": "0",
                        "guid": {
                          "id": {
                            "addr": "0xd49ac8ab4cc24d71b5164d4c02b4a38aedb95759853a4bdb77469bcadd042b6f",
                            "creation_num": "1125899906842624"
                          }
                        }
                      }
                    }
                  },
                  "type": "write_resource"
                },
                {
                  "address": "0xb3f4d0255a62f3737bb25cb9d782a6c246e9592d4d4a8846dd1c6bb2fc3f0c5e",
                  "state_key_hash": "0xa03f385e4386cfd3f77f1da910a4e949ebb365d8ac550ad5c3d98dfe49d26dae",
                  "data": {
                    "type": "0x1::fungible_asset::FungibleStore",
                    "data": {
                      "balance": "896490779",
                      "frozen": false,
                      "metadata": {
                        "inner": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b"
                      }
                    }
                  },
                  "type": "write_resource"
                },
                {
                  "address": "0xb3f4d0255a62f3737bb25cb9d782a6c246e9592d4d4a8846dd1c6bb2fc3f0c5e",
                  "state_key_hash": "0xa03f385e4386cfd3f77f1da910a4e949ebb365d8ac550ad5c3d98dfe49d26dae",
                  "data": {
                    "type": "0x1::object::ObjectCore",
                    "data": {
                      "allow_ungated_transfer": false,
                      "guid_creation_num": "1125899906842625",
                      "owner": "0xf991631f78847f14c239d0f4758cb9b69e366828decb8438319c6b84116be063",
                      "transfer_events": {
                        "counter": "0",
                        "guid": {
                          "id": {
                            "addr": "0xb3f4d0255a62f3737bb25cb9d782a6c246e9592d4d4a8846dd1c6bb2fc3f0c5e",
                            "creation_num": "1125899906842624"
                          }
                        }
                      }
                    }
                  },
                  "type": "write_resource"
                }
              ],
              "timestamp": "****************",
              "type": "user_transaction"
            }
            """;

        parseAndDisplayTransaction(swapTransactionJson, "Swap交易",
            "这是一个swap交易，涉及两种代币的交换：\n" +
            "输入代币: 0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b\n" +
            "输出代币: 0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b\n" +
            "用户地址: 0xf991631f78847f14c239d0f4758cb9b69e366828decb8438319c6b84116be063");
    }

    /**
     * 演示Transfer交易解析
     */
    private static void demonstrateTransferTransaction() {
        System.out.println("【演示2：Transfer交易解析】");
        System.out.println("交易类型：代币转账 (Token Transfer)");
        System.out.println("交易哈希：0xa3c04c7bf9e11841f8b8c11e7d1192fbbc89a17b787f992963a38f43f4f98c10");
        System.out.println();

        // 使用您提供的第二个交易数据（Transfer交易）
        String transferTransactionJson = """
            {
              "version": "3173508090",
              "hash": "0xa3c04c7bf9e11841f8b8c11e7d1192fbbc89a17b787f992963a38f43f4f98c10",
              "state_change_hash": "0x958e6c49842880d3a6ff69dac24e1f627a6df7227d3dcff8ae1c1efda2463fde",
              "event_root_hash": "0xf1a4545ce7a846da374ab63943b3ba2134af76a347b9ab7fe01437e0886871a6",
              "state_checkpoint_hash": null,
              "gas_used": "15",
              "success": true,
              "vm_status": "Executed successfully",
              "accumulator_root_hash": "0x7bb2e9bf41271aabdd27ce7b2f90af43cfb14cafce001a79db5647483937eeaa",
              "sender": "0x84b1675891d370d5de8f169031f9c3116d7add256ecf50a4bc71e3135ddba6e0",
              "sequence_number": "7083358",
              "max_gas_amount": "4000",
              "gas_unit_price": "100",
              "expiration_timestamp_secs": "1754317994",
              "payload": {
                "function": "0x1::primary_fungible_store::transfer",
                "type_arguments": ["0x1::fungible_asset::Metadata"],
                "arguments": [
                  {"inner": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b"},
                  "0x3daef4bf24b51871de4fbadab8d05731505f58a3234abd906096a4bd446f5fe6",
                  "7561700"
                ],
                "type": "entry_function_payload"
              },
              "signature": {
                "public_key": "0x5e9a2968688a26663d59693541591947b9b00f37c0ea2bed1cb7bcc90040a58e",
                "signature": "0xd5443c6565d75223fdec50206f20b3affa0ec39626d70bd3e020f1d34985784f8d8af20e6dafe66505f5282086e7d54126e9e38a881c27c59993c7805d116a00",
                "type": "ed25519_signature"
              },
              "events": [
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::fungible_asset::Withdraw",
                  "data": {
                    "amount": "7561700",
                    "store": "0x95f58b125d9f175c77ecab71b9c1195230291fb9cab86f8669d80e6061547691"
                  }
                },
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::fungible_asset::Deposit",
                  "data": {
                    "amount": "7561700",
                    "store": "0x1e2fa7d726997925bc34278481932fea82ce73c60823aefe1ba26e5e1434b907"
                  }
                },
                {
                  "guid": {"creation_number": "0", "account_address": "0x0"},
                  "sequence_number": "0",
                  "type": "0x1::transaction_fee::FeeStatement",
                  "data": {
                    "execution_gas_units": "7",
                    "io_gas_units": "9",
                    "storage_fee_octas": "0",
                    "storage_fee_refund_octas": "0",
                    "total_charge_gas_units": "15"
                  }
                }
              ],
              "changes": [
                {
                  "address": "0x95f58b125d9f175c77ecab71b9c1195230291fb9cab86f8669d80e6061547691",
                  "state_key_hash": "0x5aa8b36a101ad7f2d6b2728cfa02965c06b5878b0a50ef065f08330c42025de6",
                  "data": {
                    "type": "0x1::fungible_asset::FungibleStore",
                    "data": {
                      "balance": "**************",
                      "frozen": false,
                      "metadata": {
                        "inner": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b"
                      }
                    }
                  },
                  "type": "write_resource"
                },
                {
                  "address": "0x95f58b125d9f175c77ecab71b9c1195230291fb9cab86f8669d80e6061547691",
                  "state_key_hash": "0x5aa8b36a101ad7f2d6b2728cfa02965c06b5878b0a50ef065f08330c42025de6",
                  "data": {
                    "type": "0x1::object::ObjectCore",
                    "data": {
                      "allow_ungated_transfer": false,
                      "guid_creation_num": "1125899906842625",
                      "owner": "0x84b1675891d370d5de8f169031f9c3116d7add256ecf50a4bc71e3135ddba6e0",
                      "transfer_events": {
                        "counter": "0",
                        "guid": {
                          "id": {
                            "addr": "0x95f58b125d9f175c77ecab71b9c1195230291fb9cab86f8669d80e6061547691",
                            "creation_num": "1125899906842624"
                          }
                        }
                      }
                    }
                  },
                  "type": "write_resource"
                },
                {
                  "address": "0x1e2fa7d726997925bc34278481932fea82ce73c60823aefe1ba26e5e1434b907",
                  "state_key_hash": "0x92a4cd915d2ab6d61ccdc0303c886a675190f9f9c7de1ebd0e53b6534c3cde59",
                  "data": {
                    "type": "0x1::fungible_asset::FungibleStore",
                    "data": {
                      "balance": "7561700",
                      "frozen": false,
                      "metadata": {
                        "inner": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b"
                      }
                    }
                  },
                  "type": "write_resource"
                },
                {
                  "address": "0x1e2fa7d726997925bc34278481932fea82ce73c60823aefe1ba26e5e1434b907",
                  "state_key_hash": "0x92a4cd915d2ab6d61ccdc0303c886a675190f9f9c7de1ebd0e53b6534c3cde59",
                  "data": {
                    "type": "0x1::object::ObjectCore",
                    "data": {
                      "allow_ungated_transfer": false,
                      "guid_creation_num": "1125899906842625",
                      "owner": "0x3daef4bf24b51871de4fbadab8d05731505f58a3234abd906096a4bd446f5fe6",
                      "transfer_events": {
                        "counter": "0",
                        "guid": {
                          "id": {
                            "addr": "0x1e2fa7d726997925bc34278481932fea82ce73c60823aefe1ba26e5e1434b907",
                            "creation_num": "1125899906842624"
                          }
                        }
                      }
                    }
                  },
                  "type": "write_resource"
                }
              ],
              "timestamp": "1754274800584101",
              "type": "user_transaction"
            }
            """;

        parseAndDisplayTransaction(transferTransactionJson, "Transfer交易",
            "这是一个代币转账交易：\n" +
            "代币地址: 0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b\n" +
            "发送方: 0x84b1675891d370d5de8f169031f9c3116d7add256ecf50a4bc71e3135ddba6e0\n" +
            "接收方: 0x3daef4bf24b51871de4fbadab8d05731505f58a3234abd906096a4bd446f5fe6\n" +
            "转账金额: 7561700 (原始单位)");
    }

    /**
     * 通用的交易解析和显示方法
     */
    private static void parseAndDisplayTransaction(String transactionJson, String transactionType, String analysis) {
        try {
            // 解析JSON
            JsonNode transactionNode = objectMapper.readTree(transactionJson);

            // 执行解析
            AptosTransactionParser.ParsedTransaction result = parser.parseTransaction(transactionNode);

            // 输出解析结果
            System.out.println("=== " + transactionType + "解析结果 ===");
            System.out.println("交易哈希: " + result.getTxHash());
            System.out.println("发送者: " + result.getSender());
            System.out.println("成功状态: " + result.isSuccess());

            if (result.isSuccess()) {
                System.out.println("转移事件数量: " + result.getTransfers().size());
                System.out.println();

                for (int i = 0; i < result.getTransfers().size(); i++) {
                    AptosTransactionParser.TokenTransfer transfer = result.getTransfers().get(i);
                    System.out.println("=== 转移事件 " + (i + 1) + " ===");
                    System.out.println("类型: " + transfer.transferType());
                    System.out.println("代币地址: " + transfer.tokenAddress());
                    System.out.println("发送方: " + transfer.fromAddress());
                    System.out.println("接收方: " + transfer.toAddress());
                    System.out.println("金额: " + transfer.amount());
                    System.out.println("Store地址: " + transfer.storeAddress());
                    System.out.println();
                }

                // 分析交易
                System.out.println("=== 交易分析 ===");
                System.out.println(analysis);

            } else {
                System.out.println("解析失败: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            System.err.println("解析过程出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
