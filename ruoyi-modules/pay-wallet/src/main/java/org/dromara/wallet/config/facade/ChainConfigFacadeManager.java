package org.dromara.wallet.config.facade;

import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.BlockchainType;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.wallet.exception.WalletException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 链配置门面管理器
 * 提供统一的多链配置管理接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
public class ChainConfigFacadeManager {

    @Lazy
    @Resource
    private TronConfigFacade tronFacade;

    @Lazy
    @Resource
    private BscConfigFacade bscFacade;

    @Lazy
    @Resource
    private ArbConfigFacade arbFacade;

    @Lazy
    @Resource
    private BaseConfigFacade baseFacade;

    @Lazy
    @Resource
    private SolanaConfigFacade solanaFacade;

    @Lazy
    @Resource
    private AvaxConfigFacade avaxFacade;

    @Lazy
    @Resource
    private Map<String, Object> chainFacades;

    @PostConstruct
    public void init() {
        chainFacades = new HashMap<>();
        chainFacades.put("TRON", tronFacade);
        chainFacades.put("BSC", bscFacade);
        chainFacades.put("ARB", arbFacade);
        chainFacades.put("BASE", baseFacade);
        chainFacades.put("AVAX", avaxFacade);
        chainFacades.put("SOLANA", solanaFacade);

        log.info("链配置门面管理器初始化完成，支持的链: {}", chainFacades.keySet());

        // 验证所有配置
        validateAllChains();

        // 输出配置摘要
        logConfigSummary();
    }

    // ============ 获取门面对象 ============

    /**
     * 获取TRON配置门面
     */
    public TronConfigFacade getTron() {
        return tronFacade;
    }

    /**
     * 获取BSC配置门面
     */
    public BscConfigFacade getBsc() {
        return bscFacade;
    }

    /**
     * 获取ARB配置门面
     */
    public ArbConfigFacade getArb() {
        return arbFacade;
    }

    /**
     * 获取BASE配置门面
     */
    public BaseConfigFacade getBase() {
        return baseFacade;
    }

    /**
     * 获取Solana配置门面
     */
    public SolanaConfigFacade getSolana() {
        return solanaFacade;
    }

    /**
     * 获取AVAX配置门面
     */
    public AvaxConfigFacade getAvax() {
        return avaxFacade;
    }

    // ============ 统一操作 ============

    /**
     * 获取所有支持的链名称
     */
    public Set<String> getSupportedChains() {
        return chainFacades.keySet();
    }

    /**
     * 获取所有启用的链名称
     */
    public Set<String> getEnabledChains() {
        return chainFacades.entrySet().stream()
            .filter(entry -> {
                Object facade = entry.getValue();
                return checkEnable(facade);
            })
            .map(Map.Entry::getKey)
            .collect(java.util.stream.Collectors.toSet());
    }

    private boolean checkEnable(Object facade) {
        if (facade instanceof TronConfigFacade) {
            return ((TronConfigFacade) facade).isEnabled();
        } else if (facade instanceof BscConfigFacade) {
            return ((BscConfigFacade) facade).isEnabled();
        } else if (facade instanceof ArbConfigFacade) {
            return ((ArbConfigFacade) facade).isEnabled();
        } else if (facade instanceof BaseConfigFacade) {
            return ((BaseConfigFacade) facade).isEnabled();
        } else if (facade instanceof AvaxConfigFacade) {
            return ((AvaxConfigFacade) facade).isEnabled();
        } else if (facade instanceof SolanaConfigFacade) {
            return ((SolanaConfigFacade) facade).isEnabled();
        }
        return false;
    }

    /**
     * 检查指定链是否启用
     */
    public boolean isChainEnabled(String chainName) {
        Object facade = chainFacades.get(chainName.toUpperCase());
        if (facade == null) {
            return false;
        }

        return checkEnable(facade);
    }

    /**
     * 获取指定链的代币合约地址
     */
    public String getContractAddress(String chainName, String tokenCode) {
        Object facade = chainFacades.get(chainName.toUpperCase());
        if (facade == null) {
            return null;
        }

        if (facade instanceof TronConfigFacade) {
            return ((TronConfigFacade) facade).getContractAddress(tokenCode);
        } else if (facade instanceof BscConfigFacade) {
            return ((BscConfigFacade) facade).getContractAddress(tokenCode);
        } else if (facade instanceof ArbConfigFacade) {
            return ((ArbConfigFacade) facade).getContractAddress(tokenCode);
        } else if (facade instanceof BaseConfigFacade) {
            return ((BaseConfigFacade) facade).getContractAddress(tokenCode);
        } else if (facade instanceof AvaxConfigFacade) {
            return ((AvaxConfigFacade) facade).getContractAddress(tokenCode);
        } else if (facade instanceof SolanaConfigFacade) {
            return ((SolanaConfigFacade) facade).getContractAddress(tokenCode);
        }

        return null;
    }

    /**
     * 获取指定链的主要端点
     */
    public String getPrimaryEndpoint(String chainName) {
        Object facade = chainFacades.get(chainName.toUpperCase());
        if (facade == null) {
            return null;
        }

        if (facade instanceof TronConfigFacade) {
            return ((TronConfigFacade) facade).getPrimaryEndpoint();
        } else if (facade instanceof BscConfigFacade) {
            return ((BscConfigFacade) facade).getPrimaryEndpoint();
        } else if (facade instanceof ArbConfigFacade) {
            return ((ArbConfigFacade) facade).getPrimaryEndpoint();
        } else if (facade instanceof BaseConfigFacade) {
            return ((BaseConfigFacade) facade).getPrimaryEndpoint();
        } else if (facade instanceof AvaxConfigFacade) {
            return ((AvaxConfigFacade) facade).getPrimaryEndpoint();
        } else if (facade instanceof SolanaConfigFacade) {
            return ((SolanaConfigFacade) facade).getPrimaryEndpoint();
        }

        return null;
    }

    /**
     * 根据链类型和租户ID获取主钱包地址（归集钱包地址）
     * 统一的多链主地址获取接口
     *
     * @param chainType 链类型（如：TRON、BSC、ARB、BASE、SOLANA）
     * @param tenantId 租户ID
     * @return 主钱包地址，如果不存在则抛出异常
     * @throws WalletException 当链类型不支持或主地址不存在时抛出
     */
    public MetaMainAddress getMainAddress(String chainType, String tenantId) {
        if (chainType == null || chainType.trim().isEmpty()) {
            throw new WalletException("链类型不能为空");
        }
        if (tenantId == null || tenantId.trim().isEmpty()) {
            throw new WalletException("租户ID不能为空");
        }

        String chainName = chainType.toUpperCase();
        Object facade = chainFacades.get(chainName);
        if (facade == null) {
            throw new WalletException("不支持的链类型: " + chainType);
        }

        log.debug("获取{}链租户{}的主钱包地址", chainName, tenantId);

        try {
            if (facade instanceof TronConfigFacade) {
                return ((TronConfigFacade) facade).getWalletConfig().getMainAddress(tenantId);
            } else if (facade instanceof BscConfigFacade) {
                return ((BscConfigFacade) facade).getWalletConfig().getMainAddress(tenantId);
            } else if (facade instanceof ArbConfigFacade) {
                return ((ArbConfigFacade) facade).getWalletConfig().getMainAddress(tenantId);
            } else if (facade instanceof BaseConfigFacade) {
                return ((BaseConfigFacade) facade).getWalletConfig().getMainAddress(tenantId);
            } else if (facade instanceof AvaxConfigFacade) {
                return ((AvaxConfigFacade) facade).getWalletConfig().getMainAddress(tenantId);
            } else if (facade instanceof SolanaConfigFacade) {
                return ((SolanaConfigFacade) facade).getWalletConfig().getMainAddress(tenantId);
            } else {
                throw new WalletException("链类型 " + chainType + " 的配置门面不支持主地址获取");
            }
        } catch (WalletException e) {
            // 重新抛出钱包异常，保持原有的错误信息
            throw e;
        } catch (Exception e) {
            log.error("获取{}链租户{}的主钱包地址时发生异常", chainName, tenantId, e);
            throw new WalletException("获取" + chainName + "链主钱包地址失败: " + e.getMessage(), e);
        }
    }

    // ============ EVM配置门面获取 ============

    /**
     * 根据链名称获取EVM配置门面
     * 专门用于EVM兼容链的配置获取
     *
     * @param chainName 链名称（如：BSC、ARB、BASE等）
     * @return EVM配置门面，如果链不存在或不是EVM兼容链则返回null
     */
    public EvmConfigFacade getEvmConfigFacade(String chainName) {
        if (chainName == null || chainName.trim().isEmpty()) {
            return null;
        }

        Object facade = chainFacades.get(chainName.toUpperCase());
        if (facade instanceof EvmConfigFacade) {
            return (EvmConfigFacade) facade;
        }

        throw new WalletException("不支持的EVM链: " + chainName);
    }

    /**
     * 检查指定链是否为EVM兼容链
     * 使用 BlockchainType 枚举进行判断
     *
     * @param chainName 链名称
     * @return 是否为EVM兼容链
     */
    public boolean isEvmCompatibleChain(String chainName) {
        return BlockchainType.isEvmCompatible(chainName);
    }

    /**
     * 获取所有EVM兼容链的名称列表
     * 使用 BlockchainType 枚举提供EVM兼容链列表
     *
     * @return EVM兼容链名称列表
     */
    public Set<String> getEvmCompatibleChainNames() {
        return BlockchainType.getEvmCompatibleChainNames();
    }

    // ============ 配置验证 ============

    /**
     * 验证所有链的配置
     */
    public void validateAllChains() {
        log.info("开始验证所有链配置...");

        int validCount = 0;
        int totalCount = 0;

        for (Map.Entry<String, Object> entry : chainFacades.entrySet()) {
            String chainName = entry.getKey();
            Object facade = entry.getValue();
            totalCount++;

            try {
                if (facade instanceof TronConfigFacade tronConfig) {
                    if (tronConfig.isEnabled()) {
                        tronConfig.validateConfig();
                        validCount++;
                        log.info("✅ {} 配置验证通过", chainName);
                    } else {
                        log.info("⚠️ {} 配置已禁用，跳过验证", chainName);
                    }
                } else if (facade instanceof BscConfigFacade bscConfig) {
                    if (bscConfig.isEnabled()) {
                        bscConfig.validateConfig();
                        validCount++;
                        log.info("✅ {} 配置验证通过", chainName);
                    } else {
                        log.info("⚠️ {} 配置已禁用，跳过验证", chainName);
                    }
                } else if (facade instanceof ArbConfigFacade arbConfig) {
                    if (arbConfig.isEnabled()) {
                        arbConfig.validateConfig();
                        validCount++;
                        log.info("✅ {} 配置验证通过", chainName);
                    } else {
                        log.info("⚠️ {} 配置已禁用，跳过验证", chainName);
                    }
                } else if (facade instanceof BaseConfigFacade baseConfig) {
                    if (baseConfig.isEnabled()) {
                        baseConfig.validateConfig();
                        validCount++;
                        log.info("✅ {} 配置验证通过", chainName);
                    } else {
                        log.info("⚠️ {} 配置已禁用，跳过验证", chainName);
                    }
                } else if (facade instanceof AvaxConfigFacade avaxConfig) {
                    if (avaxConfig.isEnabled()) {
                        avaxConfig.validateConfig();
                        validCount++;
                        log.info("✅ {} 配置验证通过", chainName);
                    } else {
                        log.info("⚠️ {} 配置已禁用，跳过验证", chainName);
                    }
                } else if (facade instanceof SolanaConfigFacade solanaConfig) {
                    if (solanaConfig.isEnabled()) {
                        solanaConfig.validateConfig();
                        validCount++;
                        log.info("✅ {} 配置验证通过", chainName);
                    } else {
                        log.info("⚠️ {} 配置已禁用，跳过验证", chainName);
                    }
                }
            } catch (Exception e) {
                log.error("❌ {} 配置验证失败: {}", chainName, e.getMessage());
            }
        }

        log.info("配置验证完成: {}/{} 个链配置有效", validCount, totalCount);
    }

    /**
     * 输出配置摘要
     */
    public void logConfigSummary() {
        log.info("┌─────────────────────────────────────────────────────────────────────────────────────────┐");
        log.info("│                                   链配置摘要                                            │");
        log.info("├─────────────────────────────────────────────────────────────────────────────────────────┤");

        // 按照固定顺序显示链配置
        String[] chainOrder = {"TRON", "BSC", "ARB", "BASE", "AVAX", "SOLANA"};

        for (String chainName : chainOrder) {
            Object facade = chainFacades.get(chainName);
            if (facade == null) continue;

            try {
                String summary = null;

                if (facade instanceof TronConfigFacade tronConfig) {
                    summary = tronConfig.getConfigSummary();
                } else if (facade instanceof BscConfigFacade bscConfig) {
                    summary = bscConfig.getConfigSummary();
                } else if (facade instanceof ArbConfigFacade arbConfig) {
                    summary = arbConfig.getConfigSummary();
                } else if (facade instanceof BaseConfigFacade baseConfig) {
                    summary = baseConfig.getConfigSummary();
                } else if (facade instanceof AvaxConfigFacade avaxConfig) {
                    summary = avaxConfig.getConfigSummary();
                } else if (facade instanceof SolanaConfigFacade solanaConfig) {
                    summary = solanaConfig.getConfigSummary();
                }

                if (summary != null) {
                    log.info("│ {}: {} │", String.format("%-5s", chainName), String.format("%-80s", summary));
                }
            } catch (Exception e) {
                log.info("│ {}: {} │", String.format("%-5s", chainName), String.format("%-80s", "配置摘要获取失败 - " + e.getMessage()));
            }
        }

        log.info("├─────────────────────────────────────────────────────────────────────────────────────────┤");
        log.info("│ 启用的链: {} │", String.format("%-78s", getEnabledChains().toString()));
        log.info("└─────────────────────────────────────────────────────────────────────────────────────────┘");
    }

    // ============ 统计信息 ============

    /**
     * 获取配置统计信息
     */
    public ConfigStats getConfigStats() {
        int totalChains = chainFacades.size();
        int enabledChains = getEnabledChains().size();

        int totalTokens = 0;
        int enabledTokens = 0;

        for (Object facade : chainFacades.values()) {
            if (facade instanceof TronConfigFacade tronConfig) {
                if (tronConfig.isEnabled()) {
                    Set<String> tokens = tronConfig.getEnabledTokenSymbols();
                    totalTokens += tokens.size();
                    enabledTokens += tokens.size();
                }
            } else if (facade instanceof BscConfigFacade bscConfig) {
                if (bscConfig.isEnabled()) {
                    Set<String> tokens = bscConfig.getEnabledTokenSymbols();
                    totalTokens += tokens.size();
                    enabledTokens += tokens.size();
                }
            } else if (facade instanceof ArbConfigFacade arbConfig) {
                if (arbConfig.isEnabled()) {
                    Set<String> tokens = arbConfig.getEnabledTokenSymbols();
                    totalTokens += tokens.size();
                    enabledTokens += tokens.size();
                }
            } else if (facade instanceof BaseConfigFacade baseConfig) {
                if (baseConfig.isEnabled()) {
                    Set<String> tokens = baseConfig.getEnabledTokenSymbols();
                    totalTokens += tokens.size();
                    enabledTokens += tokens.size();
                }
            } else if (facade instanceof AvaxConfigFacade avaxConfig) {
                if (avaxConfig.isEnabled()) {
                    Set<String> tokens = avaxConfig.getEnabledTokenSymbols();
                    totalTokens += tokens.size();
                    enabledTokens += tokens.size();
                }
            } else if (facade instanceof SolanaConfigFacade solanaConfig) {
                if (solanaConfig.isEnabled()) {
                    Set<String> tokens = solanaConfig.getEnabledTokenSymbols();
                    totalTokens += tokens.size();
                    enabledTokens += tokens.size();
                }
            }
        }

        return new ConfigStats(totalChains, enabledChains, totalTokens, enabledTokens);
    }

    /**
     * 配置统计信息
     */
    public record ConfigStats(int totalChains, int enabledChains, int totalTokens, int enabledTokens) {

        @Override
        public String toString() {
            return String.format("ConfigStats{chains=%d/%d(enabled), tokens=%d/%d(enabled)}",
                enabledChains, totalChains, enabledTokens, totalTokens);
        }
    }
}
