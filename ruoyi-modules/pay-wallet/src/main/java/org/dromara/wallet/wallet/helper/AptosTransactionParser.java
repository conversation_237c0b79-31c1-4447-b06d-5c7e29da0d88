package org.dromara.wallet.wallet.helper;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Aptos交易解析器
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>解析Aptos交易中的tokenAddress、fromAddress、toAddress</li>
 *   <li>支持原生APT代币和Fungible Asset代币</li>
 *   <li>处理复杂的swap、transfer等交易类型</li>
 *   <li>基于events和changes进行智能解析</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Component
public class AptosTransactionParser {

    /**
     * 解析交易结果
     */
    public static class ParsedTransaction {
        private final String txHash;
        private final String sender;
        private final List<TokenTransfer> transfers;
        private final boolean success;
        private final String errorMessage;

        public ParsedTransaction(String txHash, String sender, List<TokenTransfer> transfers, boolean success, String errorMessage) {
            this.txHash = txHash;
            this.sender = sender;
            this.transfers = transfers;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        // Getters
        public String getTxHash() {
            return txHash;
        }

        public String getSender() {
            return sender;
        }

        public List<TokenTransfer> getTransfers() {
            return transfers;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 代币转移信息
     *
     * @param tokenAddress Getters
     * @param transferType WITHDRAW, DEPOSIT, TRANSFER
     * @param storeAddress Aptos特有的store地址
     */
    public record TokenTransfer(String tokenAddress, String fromAddress, String toAddress, String amount,
                                String transferType, String storeAddress) {

    }

    /**
     * 解析Aptos交易
     *
     * @param transactionJson 交易JSON数据
     * @return 解析结果
     */
    public ParsedTransaction parseTransaction(JsonNode transactionJson) {
        try {
            // 1. 提取基础信息
            String txHash = transactionJson.get("hash").asText();
            String sender = transactionJson.get("sender").asText();
            boolean success = transactionJson.get("success").asBoolean();

            if (!success) {
                String vmStatus = transactionJson.has("vm_status") ?
                    transactionJson.get("vm_status").asText() : "Unknown error";
                return new ParsedTransaction(txHash, sender, Collections.emptyList(), false, vmStatus);
            }

            // 2. 构建store到owner的映射关系
            Map<String, String> storeToOwnerMap = buildStoreToOwnerMapping(transactionJson);

            // 3. 构建store到token的映射关系
            Map<String, String> storeToTokenMap = buildStoreToTokenMapping(transactionJson);

            // 4. 解析events中的转移事件
            List<TokenTransfer> transfers = parseTransferEvents(transactionJson, storeToOwnerMap, storeToTokenMap);

            log.debug("Aptos交易解析完成: txHash={}, transfers={}", txHash, transfers.size());
            return new ParsedTransaction(txHash, sender, transfers, true, null);

        } catch (Exception e) {
            log.error("Aptos交易解析失败: {}", e.getMessage(), e);
            return new ParsedTransaction("", "", Collections.emptyList(), false, e.getMessage());
        }
    }

    /**
     * 构建store地址到owner地址的映射
     * 从changes中的ObjectCore资源获取owner信息
     */
    private Map<String, String> buildStoreToOwnerMapping(JsonNode transactionJson) {
        Map<String, String> storeToOwnerMap = new HashMap<>();

        JsonNode changes = transactionJson.get("changes");
        if (changes == null || !changes.isArray()) {
            return storeToOwnerMap;
        }

        for (JsonNode change : changes) {
            if (!"write_resource".equals(change.get("type").asText())) {
                continue;
            }

            JsonNode data = change.get("data");
            if (data == null || !"0x1::object::ObjectCore".equals(data.get("type").asText())) {
                continue;
            }

            String storeAddress = change.get("address").asText();
            String owner = data.get("data").get("owner").asText();
            storeToOwnerMap.put(storeAddress, owner);
        }

        log.debug("构建store到owner映射: {}", storeToOwnerMap.size());
        return storeToOwnerMap;
    }

    /**
     * 构建store地址到token地址的映射
     * 从changes中的FungibleStore资源获取token信息
     */
    private Map<String, String> buildStoreToTokenMapping(JsonNode transactionJson) {
        Map<String, String> storeToTokenMap = new HashMap<>();

        JsonNode changes = transactionJson.get("changes");
        if (changes == null || !changes.isArray()) {
            return storeToTokenMap;
        }

        for (JsonNode change : changes) {
            if (!"write_resource".equals(change.get("type").asText())) {
                continue;
            }

            JsonNode data = change.get("data");
            if (data == null || !"0x1::fungible_asset::FungibleStore".equals(data.get("type").asText())) {
                continue;
            }

            String storeAddress = change.get("address").asText();
            JsonNode metadata = data.get("data").get("metadata");
            if (metadata != null && metadata.has("inner")) {
                String tokenAddress = metadata.get("inner").asText();
                storeToTokenMap.put(storeAddress, tokenAddress);
            }
        }

        log.debug("构建store到token映射: {}", storeToTokenMap.size());
        return storeToTokenMap;
    }

    /**
     * 解析转移事件
     * 从events中提取Withdraw和Deposit事件
     */
    private List<TokenTransfer> parseTransferEvents(JsonNode transactionJson,
                                                    Map<String, String> storeToOwnerMap,
                                                    Map<String, String> storeToTokenMap) {
        List<TokenTransfer> transfers = new ArrayList<>();

        JsonNode events = transactionJson.get("events");
        if (events == null || !events.isArray()) {
            return transfers;
        }

        for (JsonNode event : events) {
            String eventType = event.get("type").asText();

            if ("0x1::fungible_asset::Withdraw".equals(eventType)) {
                TokenTransfer transfer = parseWithdrawEvent(event, storeToOwnerMap, storeToTokenMap);
                if (transfer != null) {
                    transfers.add(transfer);
                }
            } else if ("0x1::fungible_asset::Deposit".equals(eventType)) {
                TokenTransfer transfer = parseDepositEvent(event, storeToOwnerMap, storeToTokenMap);
                if (transfer != null) {
                    transfers.add(transfer);
                }
            }
        }

        log.debug("解析到转移事件: {}", transfers.size());
        return transfers;
    }

    /**
     * 解析Withdraw事件
     */
    private TokenTransfer parseWithdrawEvent(JsonNode event, Map<String, String> storeToOwnerMap,
                                             Map<String, String> storeToTokenMap) {
        JsonNode data = event.get("data");
        if (data == null) {
            return null;
        }

        String amount = data.get("amount").asText();
        String storeAddress = data.get("store").asText();

        String tokenAddress = storeToTokenMap.get(storeAddress);
        String fromAddress = storeToOwnerMap.get(storeAddress);

        if (tokenAddress == null || fromAddress == null) {
            log.warn("Withdraw事件信息不完整: store={}, token={}, from={}",
                storeAddress, tokenAddress, fromAddress);
            return null;
        }

        return new TokenTransfer(tokenAddress, fromAddress, null, amount, "WITHDRAW", storeAddress);
    }

    /**
     * 解析Deposit事件
     */
    private TokenTransfer parseDepositEvent(JsonNode event, Map<String, String> storeToOwnerMap,
                                            Map<String, String> storeToTokenMap) {
        JsonNode data = event.get("data");
        if (data == null) {
            return null;
        }

        String amount = data.get("amount").asText();
        String storeAddress = data.get("store").asText();

        String tokenAddress = storeToTokenMap.get(storeAddress);
        String toAddress = storeToOwnerMap.get(storeAddress);

        if (tokenAddress == null || toAddress == null) {
            log.warn("Deposit事件信息不完整: store={}, token={}, to={}",
                storeAddress, tokenAddress, toAddress);
            return null;
        }

        return new TokenTransfer(tokenAddress, null, toAddress, amount, "DEPOSIT", storeAddress);
    }

    /**
     * 获取APT原生代币地址
     * APT的标准地址是0xa
     */
    public static String getAptTokenAddress() {
        return "0xa";
    }

    /**
     * 判断是否为APT原生代币
     */
    public static boolean isAptToken(String tokenAddress) {
        return "0xa".equals(tokenAddress) ||
            "0x0000000000000000000000000000000000000000000000000000000000000001::aptos_coin::AptosCoin".equals(tokenAddress);
    }
}
