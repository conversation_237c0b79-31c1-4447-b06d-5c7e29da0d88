package org.dromara.wallet.config;

import lombok.Getter;

/**
 * 扁平化链类型枚举
 * 基于扁平化配置理念设计，不依赖复杂的架构分层
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Getter
public enum ChainType {

    /**
     * TRON网络
     */
    TRON("tron", "TRON", "TRX", 6, false),

    /**
     * Binance Smart Chain
     */
    BSC("bsc", "Binance Smart Chain", "BNB", 18, false),

    /**
     * Arbitrum One (Layer 2)
     */
    ARB("arb", "Arbitrum One", "ETH", 18, true),

    /**
     * Base (Layer 2)
     */
    BASE("base", "Base", "ETH", 18, true),

    /**
     * Avalanche C-Chain
     */
    AVAX("avax", "Avalanche C-Chain", "AVAX", 18, false),

    /**
     * Solana网络
     */
    SOLANA("solana", "Solana", "SOL", 9, false),;

    private final String code;
    private final String displayName;
    private final String nativeToken;
    private final int nativeDecimals;
    private final boolean isLayer2;

    ChainType(String code, String displayName, String nativeToken, int nativeDecimals, boolean isLayer2) {
        this.code = code;
        this.displayName = displayName;
        this.nativeToken = nativeToken;
        this.nativeDecimals = nativeDecimals;
        this.isLayer2 = isLayer2;
    }

    /**
     * 根据代码获取链类型
     */
    public static ChainType fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (ChainType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown chain type: " + code);
    }

    /**
     * 检查是否为支持的链类型
     */
    public static boolean isSupported(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 是否为EVM兼容链
     * 基于实际使用的技术栈判断，而不是抽象的架构概念
     */
    public boolean isEvmCompatible() {
        return this == BSC || this == ARB || this == BASE || this == AVAX;
    }

    /**
     * 是否为TRON链
     */
    public boolean isTronChain() {
        return this == TRON;
    }

    /**
     * 是否为Solana链
     */
    public boolean isSolanaChain() {
        return this == SOLANA;
    }

    /**
     * 是否为Layer 2链
     */
    public boolean isLayer2() {
        return isLayer2;
    }

    /**
     * 获取所有EVM兼容链
     */
    public static ChainType[] getEvmChains() {
        return java.util.Arrays.stream(values())
                .filter(ChainType::isEvmCompatible)
                .toArray(ChainType[]::new);
    }

    /**
     * 获取所有Layer 2链
     */
    public static ChainType[] getLayer2Chains() {
        return java.util.Arrays.stream(values())
                .filter(ChainType::isLayer2)
                .toArray(ChainType[]::new);
    }

    /**
     * 获取链的技术特征
     */
    public ChainFeatures getFeatures() {
        return switch (this) {
            case TRON -> new ChainFeatures(true, true, "energy-bandwidth", "TVM");
            case BSC, ARB, BASE, AVAX -> new ChainFeatures(true, true, "gas", "EVM");
            case SOLANA -> new ChainFeatures(true, true, "compute-unit", "Sealevel");
        };
    }

    /**
     * 链技术特征
     */
    public record ChainFeatures(
        boolean supportsSmartContracts,
        boolean usesAccountModel,
        String feeModel,
        String runtime
    ) {}

    /**
     * 获取门面类名
     * 用于动态获取对应的配置门面
     */
    public String getFacadeClassName() {
        return switch (this) {
            case TRON -> "TronConfigFacade";
            case BSC -> "BscConfigFacade";
            case ARB -> "ArbConfigFacade";
            case BASE -> "BaseConfigFacade";
            case AVAX -> "AvaxConfigFacade";
            case SOLANA -> "SolanaConfigFacade";
        };
    }

    /**
     * 是否需要私钥进行余额查询
     * 某些链的某些操作可能需要私钥
     */
    public boolean requiresPrivateKeyForBalance() {
        // 目前只有TRON和Solana在某些情况下需要私钥
        return this == TRON || this == SOLANA;
    }

    /**
     * 获取链的简短描述
     */
    public String getDescription() {
        return String.format("%s (%s) - %s%s",
            displayName,
            nativeToken,
            isLayer2 ? "Layer 2, " : "",
            getFeatures().runtime()
        );
    }

    /**
     * 验证链配置是否完整
     */
    public boolean validateChainConfig() {
        // 这里可以添加具体的配置验证逻辑
        // 例如检查对应的门面类是否存在、配置是否完整等
        return true;
    }

    @Override
    public String toString() {
        return String.format("FlatChainType{code='%s', name='%s', native='%s'}",
            code, displayName, nativeToken);
    }
}
